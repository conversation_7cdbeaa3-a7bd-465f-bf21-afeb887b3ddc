

trigger:
  branches:
    include:
      - develop

variables:
  nodeVersion: '18.x'
  artifactFeed: 'cps-ui-dev'

pool:
  name: 'CPSAPP'

jobs:
- job: BuildAndPublish
  displayName: 'Build and Publish to Azure Artifacts'
  steps:
  - task: NodeTool@0
    displayName: 'Setup Node.js'
    inputs:
      versionSpec: $(nodeVersion)

  # Reset npm registry to public and install pnpm
  - script: |
      npm config set registry https://registry.npmjs.org/
      npm install -g pnpm
    displayName: 'Install pnpm from public registry'

  # Install dependencies (before configuring Azure Artifacts)
  - script: pnpm install
    displayName: 'Install dependencies'

  # Configure npm registry for Azure Artifacts (only for publishing)
  - script: |
      npm config set registry https://pkgs.dev.azure.com/cpsAvendra/CPS.Modernization/_packaging/$(artifactFeed)/npm/registry/
      npm config set always-auth true
    displayName: 'Configure npm registry for publishing'

  - task: NpmAuthenticate@0
    inputs:
      workingFile: '$(Build.SourcesDirectory)/.npmrc'

  # Build the library
  - script: pnpm run build
    displayName: 'Build library'

  # Version and publish with proper increment from published version
  - powershell: |
      Write-Host "Current version in package.json:"
      $currentVersion = node -p "require('./package.json').version"
      Write-Host $currentVersion

      Write-Host "Checking latest published version from $(artifactFeed) feed..."
      try {
          $latestVersion = npm view common-ui version 2>$null
          if (-not $latestVersion) { $latestVersion = "0.0.22" }
      } catch {
          $latestVersion = "0.0.22"
      }
      Write-Host "Latest published version: $latestVersion"

      # Extract version parts and increment patch
      $versionParts = $latestVersion.Split('.')
      $major = [int]$versionParts[0]
      $minor = [int]$versionParts[1]
      $patch = [int]$versionParts[2]
      $newPatch = $patch + 1
      $newVersion = "$major.$minor.$newPatch"

      Write-Host "Setting new version to: $newVersion"
      npm version $newVersion --no-git-tag-version

      Write-Host "Publishing to $(artifactFeed) feed..."
      npm publish
    displayName: 'Version and Publish Package'
